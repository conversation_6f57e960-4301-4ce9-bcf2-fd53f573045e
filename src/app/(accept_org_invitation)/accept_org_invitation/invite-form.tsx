"use client";
import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { GetRequest, PostRequest } from "~/utils/new-request";
import Loading from "~/components/ui/loading";
import { useRouter, useSearchParams } from "next/navigation";

const InviteForm = () => {
  const [buttonLoading, setButtonLoading] = useState(false);
  const [marketingOptIn, setMarketingOptIn] = useState(false);
  const router = useRouter();
  const [orgData, setOrgData] = useState<any>(null);
  const searchParams = useSearchParams();
  const org_id = searchParams.get("org_id");
  const invitation_token = searchParams.get("invitation_token");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (org_id) {
      localStorage.setItem("orgId", org_id);

      const getOrgDetails = async () => {
        const res = await GetRequest(`/organisations/${org_id}/load-org-info`);
        if (res?.status === 200 || res?.status === 201) {
          setOrgData(res?.data?.data);
        }
        setLoading(false);
      };
      getOrgDetails();
    }
  }, [org_id]);

  const handleAccept = async (e: any) => {
    e.preventDefault();

    const reqBody = {
      token: invitation_token,
    };

    setButtonLoading(true);

    const res = await PostRequest("/invite/verify", reqBody);

    if (res?.status === 200 || res?.status === 201) {
      localStorage.setItem("token", res?.data?.data?.access_token);
      localStorage.setItem("user", JSON.stringify(res?.data?.data?.user));

      router.push(`/client/invited?org_id=${org_id}`);
    } else {
      setButtonLoading(false);
    }
  };

  //

  return (
    <div className="w-full pb-20">
      <div className="flex flex-col items-center justify-center w-full bg-[#faf8f6] py-10 rounded-md text-center">
        <Link href="/">
          <Image src="/login_logo.svg" alt="" width={86} height={31} />
        </Link>

        {loading ? (
          <div className="w-full h-[30vh] max-w-[48rem] mx-auto flex items-center justify-center py-[2rem]">
            <Loading height="50" width="50" color="#7141F8" />
          </div>
        ) : (
          <>
            <h1 className="text-[48px] font-bold leading-tight mt-14">
              See what{" "}
              <span className="text-[#6E1EFF] capitalize">
                {orgData?.organisation_name}
              </span>{" "}
              is up to
            </h1>

            <p className="mt-2 text-gray-700 text-base">
              Telex is where work happens for companies of all sizes.
            </p>

            {/* Avatars */}
            <div className="mt-6 flex items-center justify-center">
              <div className="flex -space-x-3">
                {orgData?.users_photos?.map((item: string, index: number) => {
                  return (
                    <Image
                      key={index}
                      src={item}
                      className="size-14 rounded-lg border-2 border-white"
                      alt="members"
                      width={100}
                      height={100}
                    />
                  );
                })}
              </div>
            </div>
            <p className="mt-1 text-base text-gray-600">
              {orgData?.org_user_info}.
            </p>
          </>
        )}
      </div>

      <div className="max-w-lg w-full mx-auto bg-white rounded-md text-center">
        <form
          onSubmit={handleAccept}
          className="text-left mt-10 space-y-5 px-8"
        >
          <p className="text-sm font-medium">
            {/* We suggest using the <strong>email account you use for work.</strong> */}
          </p>

          <button
            // type="submit"
            disabled={buttonLoading}
            className="w-full bg-blue-200 hover:bg-blue-400 text-white font-semibold py-3 rounded-md flex items-center justify-center transition"
          >
            Accept Invitation
            {buttonLoading && (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            )}
          </button>

          <div className="flex items-start gap-2 mt-3">
            <input
              id="marketing"
              type="checkbox"
              checked={marketingOptIn}
              onChange={(e) => setMarketingOptIn(e.target.checked)}
              className="mt-1"
            />
            <label htmlFor="marketing" className="text-sm text-gray-600">
              It’s okay to send me marketing communications about Salesforce,
              including Telex. I can unsubscribe at any time.
            </label>
          </div>

          <p className="text-xs text-gray-500 mt-3">
            By continuing, you’re agreeing to our{" "}
            <a
              href="/terms-of-service"
              target="_blank"
              className="text-blue-600 underline"
            >
              User Terms of Service
            </a>
            . Additional disclosures are available in our{" "}
            <a
              href={`/policy`}
              target="_blank"
              className="text-blue-600 underline"
            >
              Privacy Policy
            </a>
            .
          </p>
        </form>
      </div>
    </div>
  );
};

export default InviteForm;
