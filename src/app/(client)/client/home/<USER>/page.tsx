"use client";

import { <PERSON>R<PERSON>, Hash, ArrowDownUpIcon } from "lucide-react";
import Image from "next/image";
import React, { useContext, useEffect, useState } from "react";
import AllChannelHeader from "../../_components/channel-nav/all-channels";
import { DataContext } from "~/store/GlobalState";
import { useRouter } from "next/navigation";
import { search } from "~/utils/filter";
import images from "~/assets/images";

export default function ChannelsPage() {
  const { state } = useContext(DataContext);
  const { channels } = state;
  const router = useRouter();
  const [searchInput, setSearchInput] = useState("");

  useEffect(() => {
    localStorage.removeItem("channelName");
  }, []);

  const selectChannel = (item: any) => {
    localStorage.setItem("channelId", item?.channels_id);
    localStorage.setItem("channelName", item?.name);
    router.push(`/client/home/<USER>/${item.channels_id}`);
  };

  const channelsData = search(channels, searchInput);

  //

  return (
    <div className="flex flex-col h-[calc(100vh-70px)] relative w-full overflow-hidden">
      <AllChannelHeader />
      <div className="p-6 w-full overflow-y-auto">
        {/* Top Bar */}
        <div className="mb-4 flex items-center gap-3">
          <input
            type="text"
            placeholder="Find a channel"
            className="w-full max-w-sm px-4 py-2 border rounded-md text-sm"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
          />

          <button className="p-2 border rounded-md text-gray-600 hover:bg-gray-50">
            <ArrowDownUpIcon className="text-gray-500" size={18} />
          </button>
        </div>

        {/* Channel List */}
        {channelsData?.map((channel: any, index: number) => (
          <div
            key={index}
            className="flex items-center justify-between py-3 px-4 bg-white rounded-lg hover:bg-gray-50 transition border mb-4 cursor-pointer"
            onClick={() => selectChannel(channel)}
          >
            {/* Left section */}
            <div className="flex items-center gap-2">
              <Hash className="text-gray-500 w-4 h-4" />
              <span className="font-medium text-gray-800">#{channel.name}</span>
              <span className="text-sm text-gray-400">
                {channel.last_post_time}
              </span>
              {channel.unread_count > 0 && (
                <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-violet-100 text-violet-700 font-semibold">
                  {channel.unread_count}
                </span>
              )}
            </div>

            {/* Right section */}
            <div className="flex items-center gap-3">
              <div className="flex -space-x-1">
                {channel?.member_avatars?.map((image: string, i: number) => (
                  <Image
                    key={i}
                    src={image || images.user}
                    alt={`User ${image}`}
                    width={28}
                    height={28}
                    className="rounded-md border-2 border-white object-cover size-9"
                    style={{ zIndex: channel.member_avatars.length - i }}
                  />
                ))}
              </div>
              {
                <span className="text-sm text-gray-500">
                  {channel.members_count > 0 && "+"}
                  {channel.members_count} members
                </span>
              }
              <ArrowRight className="text-gray-300 w-4 h-4" />
            </div>
          </div>
        ))}

        {channelsData?.length === 0 && (
          <p className="text-center mt-40 text-gray-400">
            Channel not available
          </p>
        )}
      </div>
    </div>
  );
}
