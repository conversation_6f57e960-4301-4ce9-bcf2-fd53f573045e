"use client";
import { useContext, useEffect, useRef } from "react";
import { DataContext } from "~/store/GlobalState";
import axios from "axios";
import { Centrifuge } from "centrifuge";
import { ACTIONS } from "~/store/Actions";
import { ToastContainer } from "react-toastify";
import { usePathname } from "next/navigation";
import { stripHtmlTags } from "~/utils/utils";

const CLIENT_URL = process.env.NEXT_PUBLIC_CLIENT_URL;

//

const showBrowserNotification = async (
  body: string,
  url: string,
  title: string
) => {
  // Request permission from user
  if (!("Notification" in window)) {
    console.error("This browser does not support desktop notification");
    return;
  }

  console.log(url);

  await Notification.requestPermission().then((permission) => {
    if (permission === "granted") {
      const options = {
        body,
      };

      const notification = new Notification(title, options);

      notification.onclick = (e) => {
        e.preventDefault();
        window.open(url, "_blank");
      };
    }
  });
};

export default function GeneralNotificationConnection() {
  const { state, dispatch } = useContext(DataContext);
  const { orgId } = state;
  const params = usePathname();

  const routeUrl = `${CLIENT_URL}${params}`;

  const audioPlayer = useRef<any>(null);

  const connectUrl: any = process.env.NEXT_PUBLIC_CONNECT_URL;

  // get connection token
  const getConnectionToken = async () => {
    const token = localStorage.getItem("token") || "";
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/connection`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data.data.token;
  };

  //fetch subscription token
  const getSubscriptionToken = async (channel: string) => {
    const token = localStorage.getItem("token") || "";

    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/subscription`,
      { channel },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data.data.token;
  };

  // centrifugo connection for notification
  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user") || "{}");

    // Initialize Centrifuge client
    const centrifugeClient: any = new Centrifuge(connectUrl, {
      getToken: getConnectionToken,
      debug: true,
    });

    centrifugeClient.on("connect", () => {
      console.log("Connected to Centrifuge");
    });

    centrifugeClient.on("disconnect", () => {
      console.log("Disconnected from Centrifuge");
    });

    // Function to get the token for the personal channel
    const getPersonalChannelSubscriptionToken = async () => {
      return getSubscriptionToken(`${orgId}/${user?.id}`);
    };

    // Create a subscription to the channel
    const sub = centrifugeClient.newSubscription(`${orgId}/${user?.id}`, {
      getToken: getPersonalChannelSubscriptionToken,
    });

    sub.on("subscribed", () => {
      // console.log("Subscription confirmeds for general notification:", sub);
    });

    // message publishing
    sub.on("publication", (ctx: any) => {
      dispatch({ type: ACTIONS.NOTIFICATIONS, payload: ctx?.data });
      dispatch({ type: ACTIONS.NOTIFY, payload: ctx?.data?.data });
      const result = ctx?.data;
      // console.log(result);

      if (
        result?.section === "thread_message" &&
        result?.notification_type == "new_message"
      ) {
        playSound();
        showBrowserNotification(
          stripHtmlTags(result.data.message),
          routeUrl,
          result?.data?.name || result?.data?.channel_name
        );
      }

      // Thread count and mention count highlight
      if (
        result?.section === "channels_section" &&
        result?.notification_type === "unread_thread_change"
      ) {
        dispatch({
          type: ACTIONS.THREAD_COUNT,
          payload: ctx.data.data.thread_count,
        });

        dispatch({
          type: ACTIONS.UPDATE_THREAD_COUNT,
          payload: {
            channels_id: ctx.data.data.channels_id,
            mention_count: ctx.data.data.mention_count,
            thread_count: ctx.data.data.thread_count,
          },
        });
      }

      // DM notifications
      if (
        result?.section === "dm_channels_section" &&
        result?.notification_type === "unread_thread_change"
      ) {
        dispatch({
          type: ACTIONS.DM_COUNT,
          payload: ctx.data.data.thread_count,
        });

        dispatch({
          type: ACTIONS.UPDATE_DM_COUNT,
          payload: {
            channel_id: ctx.data.data.channel_id,
            thread_count: ctx.data.data.thread_count,
          },
        });
      }
    });

    sub.on("error", (ctx: any) => {
      console.error(`Subscription error: ${ctx.message}`);
    });

    // Connect to Centrifuge and subscribe
    centrifugeClient.connect();
    sub.subscribe();

    // Cleanup on component unmount
    return () => {
      sub.unsubscribe();
      centrifugeClient.disconnect();
    };
  }, [connectUrl, dispatch, orgId]);

  const playSound = () => {
    audioPlayer?.current?.play();
  };

  //

  return (
    <div>
      <ToastContainer limit={1} />
      <audio controls ref={audioPlayer} style={{ display: "none" }}>
        <source src="/audio/message.mp3" type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>
    </div>
  );
}
