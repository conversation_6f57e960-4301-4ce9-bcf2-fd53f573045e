import React, { useContext, useState } from "react";
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  ColumnDef,
  flexRender,
  SortingState,
  ColumnFiltersState,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { Button } from "~/components/ui/button";
import Icons from "~/app/(client)/client/_components/billing/icons";
import { cn } from "~/lib/utils";
// import { TelexTransaction, CreditTransactionRecord } from "../type";
import Image from "next/image";
import images from "~/assets/images";
import TableEmptyState from "../table-empty-state";
import { Member, MembersProps } from "./type";
import { Check, Pencil, Search, Trash2, UserCheck } from "lucide-react";
import { Input } from "~/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import EditMemberRoleModal from "./edit-member-role-modal";
import RestrictMemberModal from "./restrict-user-access-modal";
import { DataContext } from "~/store/GlobalState";

export default function MembersTable({
  membersData = [],
  isLoading = false,
}: MembersProps) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [isOpenEditMemberModal, setIsOpenEditMemberModal] =
    useState<boolean>(false);
  const [isOpenRestrictMemberModal, setIsOpenRestrictMemberModal] =
    useState<boolean>(false);
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);

  const columns: ColumnDef<Member>[] = [
    {
      accessorKey: "email",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium hover:bg-transparent text-[#667085]"
          >
            Email Address
            <Icons name="move-down" svgProps={{}} />
          </Button>
        );
      },
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-4">
            <div className="rounded-md h-[30px] w-[30px] cursor-pointer hover:opacity-90">
              <Image
                src={
                  row.original.profile_url != ""
                    ? row.original.profile_url
                    : images?.user
                }
                alt=""
                width={40}
                height={40}
                className="rounded-md w-full h-full"
              />
            </div>
            <span className="text-gray-700">{row.original.email}</span>
          </div>
        );
      },
      sortingFn: "alphanumeric",
    },
    {
      accessorKey: "role",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium hover:bg-transparent text-[#667085]"
          >
            Role
            <Icons name="move-down" svgProps={{}} />
          </Button>
        );
      },
      cell: ({ row }) => (
        <span className="text-gray-700">{row.getValue("role")}</span>
      ),
      sortingFn: "alphanumeric",
    },
    {
      accessorKey: "created_at",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium hover:bg-transparent text-[#667085]"
          >
            Date Joined
            <Icons name="move-down" svgProps={{}} />
          </Button>
        );
      },
      cell: ({ row }) => {
        return (
          <div className="flex flex-col items-start gap-0">
            <span className="text-gray-700">
              {new Date(row.original.created_at).toLocaleDateString("en-US", {
                month: "long",
                day: "numeric",
                year: "numeric",
              })}
            </span>
          </div>
        );
      },
      sortingFn: "datetime",
    },
    {
      accessorKey: "status",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium hover:bg-transparent text-[#667085]"
          >
            Status
            <Icons name="move-down" svgProps={{}} />
          </Button>
        );
      },
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <div
            className={cn(
              "flex gap-1 w-fit px-2 py-1 rounded-md items-center",
              {
                "border border-2 border-green-100 text-green-700":
                  row.original.status === "active",
                "border border-2 border-red-100 text-red-700":
                  row.original.status === "inactive",
                "border border-2 border-blue-100 text-blue-700":
                  row.original.status === "invited",
                "border border-2 border-yellow-100 text-yellow-700":
                  row.original.status === "pending",
                "border border-2 border-gray-100 text-gray-700":
                  row.original.status === "deactivated",
              }
            )}
          >
            <div
              className={cn("w-2 h-2 rounded-full", {
                "bg-red-500": row.original.status === "inactive",
                "bg-green-500": row.original.status === "active",
                "bg-blue-500": row.original.status === "invited",
                "bg-yellow-500": row.original.status === "pending",
                "bg-gray-500": row.original.status === "deactivated",
              })}
            />
            <span className="capitalize text-xs">{row.original.status}</span>
          </div>
        </div>
      ),
      sortingFn: "alphanumeric",
    },
    {
      accessorKey: "action",
      header: () => {
        return (
          <div className="h-auto p-0 font-medium hover:bg-transparent text-[#667085]">
            Actions
          </div>
        );
      },
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <Button
            variant={"outline"}
            className="text-gray-700 h-fit p-1"
            onClick={() => {
              setIsOpenEditMemberModal(true);
              setSelectedMember(row.original);
            }}
          >
            <Pencil size={20} />
          </Button>
          {row.original.status === "inactive" ? (
            <Button
              variant={"outline"}
              className="text-green-700 h-fit p-1"
              onClick={() => {
                setIsOpenRestrictMemberModal(true);
                setSelectedMember(row.original);
              }}
            >
              <UserCheck size={20} />
            </Button>
          ) : (
            <Button
              variant={"outline"}
              className="text-gray-700 h-fit p-1"
              onClick={() => {
                setIsOpenRestrictMemberModal(true);
                setSelectedMember(row.original);
              }}
            >
              <Trash2 size={20} />
            </Button>
          )}
        </div>
      ),
      enableSorting: false,
    },
  ];
  
  const table = useReactTable({
    data: membersData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    state: {
      sorting,
      columnFilters,
    },
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
    autoResetPageIndex: false,
  });

  const currentPage = table.getState().pagination.pageIndex + 1;
  const totalPages = table.getPageCount();
  const hasData = membersData.length > 0;
  const { state } = useContext(DataContext);
  const { orgRoles } = state;

  if (isLoading) {
    return (
      <div className="w-full mx-auto bg-white border mt-6 rounded-xl overflow-hidden">
        <div className="flex items-center justify-center py-16">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-500">Loading payment history...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full mx-auto  bg-white border  mt-6 rounded-xl overflow-hidden">
      <EditMemberRoleModal
        isOpen={isOpenEditMemberModal}
        onClose={() => setIsOpenEditMemberModal(false)}
        selectedMember={selectedMember}
      />
      <RestrictMemberModal
        isOpen={isOpenRestrictMemberModal}
        onClose={() => setIsOpenRestrictMemberModal(false)}
        selectedMember={selectedMember}
      />
      {hasData ? (
        <>
          <div className="flex items-center justify-between px-6 py-4 border-b">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400"
                  size={16}
                />
                <Input
                  placeholder="Search by name or email"
                  value={
                    (table.getColumn("email")?.getFilterValue() as string) ?? ""
                  }
                  onChange={(event) =>
                    table.getColumn("email")?.setFilterValue(event.target.value)
                  }
                  className="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-neutral-400 text-neutral-800 w-[300px]"
                />
              </div>
              <Select
                value={
                  (table.getColumn("role")?.getFilterValue() as string) ?? "all"
                }
                onValueChange={(value) =>
                  table
                    .getColumn("role")
                    ?.setFilterValue(value === "all" ? "" : value)
                }
                defaultValue="all"
              >
                <SelectTrigger className="w-fit gap-3">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  {orgRoles &&
                    orgRoles.map((role: any) => (
                      <SelectItem key={role.id} value={role.name.toLowerCase()}>
                        {role.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="border-b">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id} className="bg-gray-50">
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className="font-medium text-gray-700"
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="hover:bg-gray-50"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="py-4">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between px-6 space-x-2 py-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className="flex items-center gap-2 border-gray-300"
            >
              <Icons name="move-left" svgProps={{}} />
              Previous
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                let pageNum: number;
                if (totalPages <= 7) {
                  pageNum = i + 1;
                } else if (currentPage <= 4) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 3) {
                  pageNum = totalPages - 6 + i;
                } else {
                  pageNum = currentPage - 3 + i;
                }

                if (pageNum === 4 && currentPage > 4 && totalPages > 7) {
                  return (
                    <span key="ellipsis1" className="px-2">
                      ...
                    </span>
                  );
                }
                if (
                  pageNum === totalPages - 3 &&
                  currentPage < totalPages - 3 &&
                  totalPages > 7
                ) {
                  return (
                    <span key="ellipsis2" className="px-2">
                      ...
                    </span>
                  );
                }

                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => table.setPageIndex(pageNum - 1)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className="flex items-center gap-2 border-gray-300"
            >
              Next
              <Icons name="move-right" svgProps={{}} />
            </Button>
          </div>
        </>
      ) : (
        <TableEmptyState
          title="No members found."
          description="Your members will appear here once you invite them."
        />
      )}
    </div>
  );
}
